import 'package:flutter/material.dart';

class WebPOReviewLeftPanel extends StatefulWidget {
  const WebPOReviewLeftPanel({Key? key}) : super(key: key);

  @override
  State<WebPOReviewLeftPanel> createState() => _WebPOReviewLeftPanelState();
}

class _WebPOReviewLeftPanelState extends State<WebPOReviewLeftPanel> {
  // Checkbox states
  bool budgetApprovalRequired = false;
  bool budgetIndicatorAvailable = false;
  bool approvalAuthorityVerified = false;
  bool approvalAuthorityWithinLimit = false;
  bool vendorInformationVerified = false;
  bool deliveryDateAcceptable = false;
  bool paymentTermsAcceptable = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Section
          _buildHeader(),
          
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Order Summary Section
                  _buildOrderSummarySection(),
                  
                  const SizedBox(height: 20),
                  
                  // Line Items Section
                  _buildLineItemsSection(),
                  
                  const SizedBox(height: 20),
                  
                  // Subtotal Section
                  _buildSubtotalSection(),
                  
                  const SizedBox(height: 20),
                  
                  // Approval Checklist Section
                  _buildApprovalChecklistSection(),
                  
                  const SizedBox(height: 20),
                  
                  // Action Buttons
                  _buildActionButtons(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 18,
            height: 18,
            decoration: BoxDecoration(
              color: Colors.green.shade600,
              borderRadius: BorderRadius.circular(2),
            ),
            child: const Icon(
              Icons.check,
              color: Colors.white,
              size: 12,
            ),
          ),
          const SizedBox(width: 8),
          const Text(
            'PURCHASE ORDER REVIEW',
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderSummarySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // PO Number Section with light blue background
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: const Color(0xFFE3F2FD),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Column(
            children: [
              Text(
                'PO-2024-0145',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1976D2),
                ),
              ),
              SizedBox(height: 4),
              Text(
                'Ready for Approval • Created Jan 15, 2024',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 20),
        
        // Order Summary Header
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.assignment_outlined,
                  size: 18,
                  color: Colors.grey.shade700,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Order Summary',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              height: 1,
              width: double.infinity,
              color: Colors.grey.shade300,
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Summary Details in 2x2 grid
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard('VENDOR', 'TechSupply Corp'),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSummaryCard('EXPECTED DELIVERY', 'Feb 1, 2024'),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard('PAYMENT TERMS', 'Net 30'),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildSummaryCard('PRIORITY', 'Standard'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String label, String value) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border(
          left: BorderSide(color: Colors.blue.shade600, width: 3),
          top: BorderSide(color: Colors.grey.shade200, width: 1),
          right: BorderSide(color: Colors.grey.shade200, width: 1),
          bottom: BorderSide(color: Colors.grey.shade200, width: 1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade600,
              letterSpacing: 0.5,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLineItemsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.folder_outlined,
                  size: 16,
                  color: Colors.brown.shade600,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Line Items (3 items)',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              height: 1,
              width: double.infinity,
              color: Colors.grey.shade300,
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        // Line Items
        _buildLineItem('Laptop - Dell XPS 13', 'Qty: 5 • \$1,200.00', '\$6,000.00'),
        const Divider(height: 1, color: Colors.grey),
        _buildLineItem('Monitor - 24 inch', 'Qty: 10 • \$300.00', '\$3,000.00'),
        const Divider(height: 1, color: Colors.grey),
        _buildLineItem('Wireless Mouse', 'Qty: 15 • \$35.00', '\$525.00'),
      ],
    );
  }

  Widget _buildLineItem(String title, String quantity, String amount) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w500,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                quantity,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
              Text(
                amount,
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1976D2),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSubtotalSection() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: Column(
        children: [
          _buildSubtotalRow('Subtotal', '\$6,399.85'),
          _buildSubtotalRow('Tax (8%)', '\$511.99'),
          _buildSubtotalRow('Shipping', '\$25.00'),
          const Divider(height: 16),
          _buildSubtotalRow('Total', '\$6,936.84', isTotal: true),
        ],
      ),
    );
  }

  Widget _buildSubtotalRow(String label, String amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: isTotal ? 13 : 12,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: Colors.black87,
            ),
          ),
          Text(
            amount,
            style: TextStyle(
              fontSize: isTotal ? 13 : 12,
              fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildApprovalChecklistSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Approval Checklist',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        
        const SizedBox(height: 12),
        
        _buildCheckboxItem(
          'Budget Approval Required (25% of remaining budget)',
          budgetApprovalRequired,
          (value) => setState(() => budgetApprovalRequired = value ?? false),
        ),
        _buildCheckboxItem(
          'Budget Indicator Available',
          budgetIndicatorAvailable,
          (value) => setState(() => budgetIndicatorAvailable = value ?? false),
        ),
        _buildCheckboxItem(
          'Approval Authority Verified',
          approvalAuthorityVerified,
          (value) => setState(() => approvalAuthorityVerified = value ?? false),
        ),
        _buildCheckboxItem(
          'Approval Authority Within your approval limit (100%)',
          approvalAuthorityWithinLimit,
          (value) => setState(() => approvalAuthorityWithinLimit = value ?? false),
        ),
        
        const SizedBox(height: 12),
        
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.orange.shade50,
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: Colors.orange.shade200),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.checklist_outlined,
                    size: 14,
                    color: Colors.orange.shade700,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'Price Review Checklist',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Colors.orange.shade700,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              _buildCheckboxItem(
                'Vendor information is verified',
                vendorInformationVerified,
                (value) => setState(() => vendorInformationVerified = value ?? false),
                isSmall: true,
              ),
              _buildCheckboxItem(
                'Delivery date is acceptable',
                deliveryDateAcceptable,
                (value) => setState(() => deliveryDateAcceptable = value ?? false),
                isSmall: true,
              ),
              _buildCheckboxItem(
                'Payment terms is acceptable',
                paymentTermsAcceptable,
                (value) => setState(() => paymentTermsAcceptable = value ?? false),
                isSmall: true,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCheckboxItem(String text, bool value, ValueChanged<bool?> onChanged, {bool isSmall = false}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: isSmall ? 2 : 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: Checkbox(
              value: value,
              onChanged: onChanged,
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              visualDensity: VisualDensity.compact,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: isSmall ? 11 : 12,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // Edit Details Button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: () {},
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              side: BorderSide(color: Colors.grey.shade400),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            child: const Text(
              'Edit Details',
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 8),
        
        // Save as Draft Button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: () {},
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              side: BorderSide(color: Colors.grey.shade400),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            child: const Text(
              'Save as Draft',
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
          ),
        ),
        
        const SizedBox(height: 8),
        
        // Submit for Approval Button
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {},
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green.shade600,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(4),
              ),
            ),
            child: const Text(
              'Submit for Approval',
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
